#!/usr/bin/env python3
"""
Main MCP server implementation for Zoho CRM
Handles MCP protocol communication and tool routing.
"""

import json
import logging
from typing import Dict, Any, List

import mcp.types as types
from mcp.server import Server
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from starlette.routing import Route
from starlette.requests import Request
from starlette.responses import Response, JSONResponse

from .config import Settings
from .auth import Auth<PERSON>and<PERSON>, AuthenticationError, set_access_token
from .zoho_api import ZohoAPIClient
from .tools import ToolDefinitions, ToolHandlers

logger = logging.getLogger(__name__)


class HandleStreamableHttp:
    """Custom ASGI endpoint for handling streamable HTTP requests and extracting headers"""

    def __init__(self, session_manager: StreamableHTTPSessionManager):
        self.session_manager = session_manager

    def _extract_headers(self, scope):
        """Extract authorization headers from request scope."""
        headers = dict(scope.get("headers", []))

        # Extract Authorization header (Bearer token)
        access_token = None
        auth_header = headers.get(b"authorization")
        if auth_header:
            auth_str = auth_header.decode("utf-8")
            if auth_str.startswith("Bearer "):
                access_token = auth_str[7:].strip()
                logger.debug("Bearer token extracted from Authorization header")

        return access_token

    async def __call__(self, scope, receive, send):
        """Handle incoming HTTP requests and extract authentication"""
        if self.session_manager is not None:
            try:
                logger.debug("Handling Streamable HTTP connection...")

                # Extract headers
                access_token = self._extract_headers(scope)

                # Set context variable for access token (if provided)
                if access_token:
                    set_access_token(access_token)
                    logger.debug("Access token set in context")

                await self.session_manager.handle_request(scope, receive, send)
                logger.debug("Streamable HTTP connection closed...")
            except Exception as e:
                logger.error(f"Error handling streamable HTTP request: {e}")
                # Send error response
                response = {
                    "type": "http.response.start",
                    "status": 500,
                    "headers": [(b"content-type", b"application/json")],
                }
                await send(response)
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps({"error": str(e)}).encode(),
                    }
                )
        else:
            # No session manager available
            response = {
                "type": "http.response.start",
                "status": 503,
                "headers": [(b"content-type", b"application/json")],
            }
            await send(response)
            await send(
                {
                    "type": "http.response.body",
                    "body": json.dumps(
                        {"error": "Session manager not available"}
                    ).encode(),
                }
            )


class ZohoCRMMCPServer:
    """Main Zoho CRM MCP Server class"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self.auth_handler = AuthHandler(settings.security)
        self.zoho_client = ZohoAPIClient(settings.zoho)
        self.tool_handlers = ToolHandlers(self.zoho_client, settings)

        # Initialize MCP server
        self.server = Server(self.settings.server.name)
        self._setup_handlers()

        logger.info(f"Zoho CRM MCP Server initialized: {settings.server.name}")

    def _setup_handlers(self):
        """Setup MCP server handlers"""

        @self.server.list_tools()
        async def handle_list_tools() -> List[types.Tool]:
            """Handle tools/list requests"""
            logger.debug("Listing available tools")
            return ToolDefinitions.get_all_tools()

        @self.server.call_tool()
        async def handle_call_tool(
            name: str, arguments: dict
        ) -> List[types.TextContent]:
            """Handle tools/call requests"""
            logger.info(f"Tool called: {name}")

            try:
                # Get access token from context (only required for certain tools)
                access_token = None
                try:
                    access_token = self.auth_handler.get_access_token()
                except AuthenticationError:
                    # No token available - check if this tool requires authentication
                    if name not in ["health_check", "get_tool_info"]:
                        # Tool requires authentication but no token provided
                        error_msg = {
                            "error": {
                                "code": "AUTHENTICATION_REQUIRED",
                                "message": "Authentication required to access this resource",
                                "details": {
                                    "authentication_requirements": [
                                        {
                                            "provider": "zoho",
                                            "auth_type": "bearer",
                                            "header_name": "Authorization",
                                            "header_format": "Bearer {access_token}",
                                            "required_scopes": [
                                                "ZohoCRM.modules.ALL",
                                                "ZohoCRM.settings.READ",
                                            ],
                                            "token_source": "access_token",
                                        },
                                    ],
                                },
                            }
                        }
                        raise Exception(json.dumps(error_msg))

                # Route to appropriate handler
                if name == "create_record":
                    return await self.tool_handlers.handle_create_record(
                        arguments, access_token
                    )
                elif name == "get_records":
                    return await self.tool_handlers.handle_get_records(
                        arguments, access_token
                    )
                elif name == "search_records":
                    return await self.tool_handlers.handle_search_records(
                        arguments, access_token
                    )
                elif name == "health_check":
                    return await self.tool_handlers.handle_health_check(arguments)
                elif name == "get_tool_info":
                    return await self.tool_handlers.handle_get_tool_info(arguments)
                else:
                    logger.warning(f"Unknown tool requested: {name}")
                    return [
                        types.TextContent(
                            type="text",
                            text=json.dumps(
                                {
                                    "success": False,
                                    "error": "UNKNOWN_TOOL",
                                    "message": f"Unknown tool: {name}",
                                    "available_tools": [
                                        tool.name
                                        for tool in ToolDefinitions.get_all_tools()
                                    ],
                                },
                                indent=2,
                            ),
                        )
                    ]

            except Exception as e:
                logger.error(f"Error in tool call {name}: {e}")
                # Let the MCP framework handle the error response with isError: true
                raise e

    def create_app(self) -> Starlette:
        """Create the ASGI application"""
        import contextlib
        from starlette.routing import Route

        # Create session manager
        session_manager = None
        try:
            session_manager = StreamableHTTPSessionManager(self.server)
            logger.info("StreamableHTTPSessionManager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
            session_manager = None

        # Create routes
        routes = []

        # Add Streamable HTTP route if available
        if session_manager is not None:
            routes.append(
                Route(
                    "/mcp",
                    endpoint=HandleStreamableHttp(session_manager),
                    methods=["POST"],
                )
            )

        # CORS middleware configuration
        cors_middleware = Middleware(
            CORSMiddleware,
            allow_origins=self.settings.cors.allow_origins,
            allow_methods=self.settings.cors.allow_methods,
            allow_headers=self.settings.cors.allow_headers,
            allow_credentials=self.settings.cors.allow_credentials,
            max_age=self.settings.cors.max_age,
        )

        # Define lifespan for session manager
        @contextlib.asynccontextmanager
        async def lifespan(app):
            """Context manager for session manager."""
            if session_manager is not None:
                async with session_manager.run():
                    logger.info(
                        "Application started with StreamableHTTP session manager!"
                    )
                    try:
                        yield
                    finally:
                        logger.info("Application shutting down...")
            else:
                yield

        # Create Starlette app
        app = Starlette(
            routes=routes,
            middleware=[cors_middleware],
            debug=self.settings.server.debug,
            lifespan=lifespan,
        )

        logger.info("ASGI application created with StreamableHTTP session manager")
        return app

    def get_server_info(self) -> Dict[str, Any]:
        """Get server information"""
        return {
            "name": self.settings.server.name,
            "version": self.settings.server.version,
            "host": self.settings.server.host,
            "port": self.settings.server.port,
            "debug": self.settings.server.debug,
            "zoho_environment": self.settings.zoho.environment,
            "zoho_api_url": self.settings.zoho.api_base_url,
            "authentication_required": self.settings.security.require_auth,
            "available_tools": [tool.name for tool in ToolDefinitions.get_all_tools()],
        }
