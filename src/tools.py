#!/usr/bin/env python3
"""
MCP Tools definitions and handlers for Zoho CRM operations
"""

import json
import logging
from typing import Dict, List, Any

import mcp.types as types
from .zoho_api import ZohoAPIClient
from .config import Settings

logger = logging.getLogger(__name__)


class ToolDefinitions:
    """Tool definitions for the MCP server"""

    # ──────────────────────────────────────────────────────────────────────
    # Public helper
    # ──────────────────────────────────────────────────────────────────────
    @staticmethod
    def get_all_tools() -> List[types.Tool]:
        """Return every tool definition this server exposes."""
        return [
            ToolDefinitions.create_record_tool(),
            ToolDefinitions.get_records_tool(),
            ToolDefinitions.search_records_tool(),
            ToolDefinitions.health_check_tool(),
            ToolDefinitions.get_tool_info_tool(),
        ]

    # ──────────────────────────────────────────────────────────────────────
    # Individual tool builders
    # ──────────────────────────────────────────────────────────────────────
    @staticmethod
    def create_record_tool() -> types.Tool:
        """Tool for creating new CRM records."""
        return types.Tool(
            name="create_record",
            title="Create CRM Record",
            description=(
                "Create a new record in a Zoho CRM module.\n\n"
                "Required fields by module:\n"
                "• Leads: Last_Name, Company\n"
                "• Contacts: Last_Name\n"
                "• Accounts: Account_Name\n"
                "• Deals: Deal_Name, Stage, Closing_Date\n\n"
                "Common optional fields:\n"
                "• Email, Phone, Mobile, Website, Description\n"
                "• Lead_Source (for Leads), Industry, Annual_Revenue"
            ),
            inputSchema={
                "type": "object",
                "properties": {
                    "module": {
                        "type": "string",
                        "description": "Target Zoho CRM module",
                        "enum": [
                            "Leads",
                            "Contacts",
                            "Accounts",
                            "Deals",
                            "Tasks",
                            "Events",
                            "Calls",
                        ],
                    },
                    "record_data": {
                        "type": "object",
                        "description": "Field/value pairs for the new record",
                        "properties": {
                            "Last_Name": {"type": "string"},
                            "First_Name": {"type": "string"},
                            "Company": {"type": "string"},
                            "Account_Name": {"type": "string"},
                            "Email": {"type": "string"},
                            "Phone": {"type": "string"},
                            "Mobile": {"type": "string"},
                            "Website": {"type": "string"},
                            "Lead_Source": {"type": "string"},
                            "Industry": {"type": "string"},
                            "Annual_Revenue": {"type": "number"},
                            "Description": {"type": "string"},
                            "Subject": {"type": "string"},
                            "Due_Date": {"type": "string"},
                            "Start_DateTime": {"type": "string"},
                            "End_DateTime": {"type": "string"},
                        },
                        "minProperties": 1,
                        # ────── OPENAI REQUIREMENT ──────
                        "additionalProperties": False,
                    },
                },
                "required": ["module", "record_data"],
                # Root-level lock-down (already met the rule, kept for clarity)
                "additionalProperties": False,
            },
        )

    @staticmethod
    def get_records_tool() -> types.Tool:
        """Tool for retrieving CRM records."""
        return types.Tool(
            name="get_records",
            title="Get CRM Records",
            description="Retrieve records with optional field filtering.",
            inputSchema={
                "type": "object",
                "properties": {
                    "module": {
                        "type": "string",
                        "enum": [
                            "Leads",
                            "Contacts",
                            "Accounts",
                            "Deals",
                            "Tasks",
                            "Events",
                            "Calls",
                        ],
                    },
                    "fields": {
                        "type": "array",
                        "items": {"type": "string"},
                        "uniqueItems": True,
                    },
                    "page": {
                        "type": "integer",
                        "minimum": 1,
                        "default": 1,
                    },
                    "per_page": {
                        "type": "integer",
                        "minimum": 1,
                        "maximum": 200,
                        "default": 50,
                    },
                },
                "required": ["module"],
                "additionalProperties": False,
            },
        )

    @staticmethod
    def search_records_tool() -> types.Tool:
        """Tool for searching CRM records."""
        return types.Tool(
            name="search_records",
            title="Search CRM Records",
            description="Search records using Zoho-style criteria.",
            inputSchema={
                "type": "object",
                "properties": {
                    "module": {
                        "type": "string",
                        "enum": [
                            "Leads",
                            "Contacts",
                            "Accounts",
                            "Deals",
                            "Tasks",
                            "Events",
                            "Calls",
                        ],
                    },
                    "search_criteria": {
                        "type": "string",
                        "pattern": r"^\(.+\)$",
                        "minLength": 3,
                    },
                    "page": {
                        "type": "integer",
                        "minimum": 1,
                        "default": 1,
                    },
                    "per_page": {
                        "type": "integer",
                        "minimum": 1,
                        "maximum": 200,
                        "default": 200,
                    },
                },
                "required": ["module", "search_criteria"],
                "additionalProperties": False,
            },
        )

    @staticmethod
    def health_check_tool() -> types.Tool:
        """Tool for server health check."""
        return types.Tool(
            name="health_check",
            title="Health Check",
            description="Verify server health and Zoho connectivity.",
            inputSchema={
                "type": "object",
                "properties": {},
                "additionalProperties": False,
            },
        )

    @staticmethod
    def get_tool_info_tool() -> types.Tool:
        """Tool for obtaining tool metadata."""
        return types.Tool(
            name="get_tool_info",
            title="Get Tool Information",
            description="Return detailed parameter specs for any tool.",
            inputSchema={
                "type": "object",
                "properties": {
                    "tool_name": {
                        "type": "string",
                        "enum": [
                            "create_record",
                            "get_records",
                            "search_records",
                            "health_check",
                            "get_tool_info",
                        ],
                    },
                },
                "additionalProperties": False,
            },
        )


class ToolHandlers:
    """Tool handlers for processing MCP tool calls"""

    def __init__(self, zoho_client: ZohoAPIClient, settings: Settings):
        self.zoho_client = zoho_client
        self.settings = settings

    async def handle_create_record(
        self, arguments: Dict[str, Any], access_token: str
    ) -> List[types.TextContent]:
        """Handle create_record tool call"""
        try:
            module = arguments.get("module")
            record_data = arguments.get("record_data", {})

            if not module or not record_data:
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": False,
                                "error": "MISSING_PARAMETERS",
                                "message": "Both 'module' and 'record_data' are required",
                            },
                            indent=2,
                        ),
                    )
                ]

            result = await self.zoho_client.create_record(
                module, record_data, access_token
            )
            return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

        except Exception as e:
            logger.error(f"Error in create_record: {e}")
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": False,
                            "error": "INTERNAL_ERROR",
                            "message": str(e),
                        },
                        indent=2,
                    ),
                )
            ]

    async def handle_get_records(
        self, arguments: Dict[str, Any], access_token: str
    ) -> List[types.TextContent]:
        """Handle get_records tool call"""
        try:
            module = arguments.get("module")
            fields = arguments.get("fields")
            page = arguments.get("page", 1)
            per_page = arguments.get("per_page", 50)

            if not module:
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": False,
                                "error": "MISSING_PARAMETERS",
                                "message": "'module' parameter is required",
                            },
                            indent=2,
                        ),
                    )
                ]

            result = await self.zoho_client.get_records(
                module, access_token, fields, page, per_page
            )
            return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

        except Exception as e:
            logger.error(f"Error in get_records: {e}")
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": False,
                            "error": "INTERNAL_ERROR",
                            "message": str(e),
                        },
                        indent=2,
                    ),
                )
            ]

    async def handle_search_records(
        self, arguments: Dict[str, Any], access_token: str
    ) -> List[types.TextContent]:
        """Handle search_records tool call"""
        try:
            module = arguments.get("module")
            search_criteria = arguments.get("search_criteria")
            page = arguments.get("page", 1)
            per_page = arguments.get("per_page", 200)

            if not module or not search_criteria:
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": False,
                                "error": "MISSING_PARAMETERS",
                                "message": "Both 'module' and 'search_criteria' are required",
                            },
                            indent=2,
                        ),
                    )
                ]

            result = await self.zoho_client.search_records(
                module, search_criteria, access_token, page, per_page
            )
            return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

        except Exception as e:
            logger.error(f"Error in search_records: {e}")
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": False,
                            "error": "INTERNAL_ERROR",
                            "message": str(e),
                        },
                        indent=2,
                    ),
                )
            ]

    async def handle_health_check(
        self, _arguments: Dict[str, Any]
    ) -> List[types.TextContent]:
        """Handle health_check tool call"""
        try:
            health_info = self.zoho_client.get_health_info()
            result = {
                "status": "healthy",
                "service": self.settings.server.name,
                "version": self.settings.server.version,
                "server_url": self.settings.get_server_url(),
                "zoho_api": health_info,
                "authentication": {
                    "required": self.settings.security.require_auth,
                    "header": self.settings.security.auth_header_name,
                    "scheme": self.settings.security.auth_scheme,
                },
            }
            return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

        except Exception as e:
            logger.error(f"Error in health_check: {e}")
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": False,
                            "error": "INTERNAL_ERROR",
                            "message": str(e),
                        },
                        indent=2,
                    ),
                )
            ]

    async def handle_get_tool_info(
        self, arguments: Dict[str, Any]
    ) -> List[types.TextContent]:
        """Handle get_tool_info tool call"""
        try:
            tool_name = arguments.get("tool_name")

            # Define detailed tool information
            tool_info = {
                "create_record": {
                    "name": "create_record",
                    "description": "Create a new record in Zoho CRM module",
                    "required_parameters": ["module", "record_data"],
                    "optional_parameters": [],
                    "module_requirements": {
                        "Leads": {
                            "required_fields": ["Last_Name", "Company"],
                            "common_optional": [
                                "Email",
                                "Phone",
                                "Mobile",
                                "Lead_Source",
                                "Website",
                                "Industry",
                                "Description",
                            ],
                        },
                        "Contacts": {
                            "required_fields": ["Last_Name"],
                            "common_optional": [
                                "First_Name",
                                "Email",
                                "Phone",
                                "Mobile",
                                "Account_Name",
                                "Title",
                                "Department",
                            ],
                        },
                        "Accounts": {
                            "required_fields": ["Account_Name"],
                            "common_optional": [
                                "Website",
                                "Industry",
                                "Annual_Revenue",
                                "Phone",
                                "Billing_Street",
                                "Billing_City",
                            ],
                        },
                        "Deals": {
                            "required_fields": ["Deal_Name", "Stage", "Closing_Date"],
                            "common_optional": [
                                "Amount",
                                "Account_Name",
                                "Contact_Name",
                                "Lead_Source",
                                "Description",
                            ],
                        },
                    },
                    "example": {
                        "module": "Leads",
                        "record_data": {
                            "Last_Name": "Smith",
                            "Company": "Acme Corp",
                            "Email": "<EMAIL>",
                            "Phone": "******-0123",
                            "Lead_Source": "Website",
                        },
                    },
                },
                "get_records": {
                    "name": "get_records",
                    "description": "Retrieve records from Zoho CRM with pagination and field selection",
                    "required_parameters": ["module"],
                    "optional_parameters": ["fields", "page", "per_page"],
                    "parameter_details": {
                        "module": "CRM module name (Leads, Contacts, Accounts, Deals, Tasks, Events, Calls)",
                        "fields": "Array of field names to retrieve (if not provided, returns all fields)",
                        "page": "Page number (default: 1, min: 1)",
                        "per_page": "Records per page (default: 50, max: 200)",
                    },
                    "common_fields": {
                        "Leads": [
                            "Last_Name",
                            "Company",
                            "Email",
                            "Phone",
                            "Lead_Source",
                            "Created_Time",
                        ],
                        "Contacts": [
                            "Last_Name",
                            "First_Name",
                            "Email",
                            "Phone",
                            "Account_Name",
                        ],
                        "Accounts": [
                            "Account_Name",
                            "Website",
                            "Industry",
                            "Annual_Revenue",
                        ],
                        "Deals": [
                            "Deal_Name",
                            "Amount",
                            "Stage",
                            "Closing_Date",
                            "Account_Name",
                        ],
                    },
                    "example": {
                        "module": "Leads",
                        "fields": [
                            "Last_Name",
                            "Company",
                            "Email",
                            "Phone",
                            "Created_Time",
                        ],
                        "page": 1,
                        "per_page": 50,
                    },
                },
                "search_records": {
                    "name": "search_records",
                    "description": "Search records using advanced criteria",
                    "required_parameters": ["module", "search_criteria"],
                    "optional_parameters": ["page", "per_page"],
                    "search_operators": {
                        "equals": "Exact match",
                        "contains": "Partial match (case-insensitive)",
                        "starts_with": "Begins with value",
                        "ends_with": "Ends with value",
                        "less_than": "Numeric/date comparison",
                        "greater_than": "Numeric/date comparison",
                    },
                    "criteria_format": {
                        "single": "(Field_Name:operator:value)",
                        "multiple": "((Field1:equals:value1)and(Field2:contains:value2))",
                        "date_example": "(Created_Time:greater_than:2024-01-01T00:00:00Z)",
                    },
                    "example": {
                        "module": "Leads",
                        "search_criteria": "((Last_Name:contains:Smith)and(Lead_Source:equals:Website))",
                        "page": 1,
                        "per_page": 100,
                    },
                },
                "health_check": {
                    "name": "health_check",
                    "description": "Check server health and configuration",
                    "required_parameters": [],
                    "optional_parameters": [],
                    "returns": [
                        "status",
                        "service",
                        "version",
                        "server_url",
                        "zoho_api",
                        "authentication",
                    ],
                    "example": {},
                },
            }

            if tool_name:
                if tool_name in tool_info:
                    result = {"tool_info": tool_info[tool_name], "success": True}
                else:
                    result = {
                        "error": f"Tool '{tool_name}' not found",
                        "available_tools": list(tool_info.keys()),
                        "success": False,
                    }
            else:
                result = {
                    "all_tools": tool_info,
                    "summary": {
                        "total_tools": len(tool_info),
                        "tool_names": list(tool_info.keys()),
                    },
                    "success": True,
                }

            return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

        except Exception as e:
            logger.error(f"Error in get_tool_info: {e}")
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": False,
                            "error": "INTERNAL_ERROR",
                            "message": str(e),
                        },
                        indent=2,
                    ),
                )
            ]
