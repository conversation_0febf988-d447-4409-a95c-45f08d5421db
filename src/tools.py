#!/usr/bin/env python3
"""
Legacy tool definitions for backward compatibility
Note: This file is kept for compatibility but the actual tool definitions
are now handled directly in server.py using the new constants structure.
"""

import logging
from typing import List

import mcp.types as types
from .constants.schema import *
from .constants.enum import Tools

logger = logging.getLogger(__name__)


class ToolDefinitions:
    """Legacy tool definitions class - kept for backward compatibility"""

    @staticmethod
    def get_all_tools() -> List[types.Tool]:
        """Return every tool definition this server exposes."""
        return [
            # CRM record management tools
            types.Tool(
                name=Tools.CREATE_RECORD,
                description="Create a new record in a Zoho CRM module. Required fields by module: Leads (Last_Name, Company), Contacts (Last_Name), Accounts (Account_Name), Deals (Deal_Name, Stage, Closing_Date)",
                inputSchema=CreateRecord.model_json_schema(),
            ),
            types.Tool(
                name=Tools.GET_RECORDS,
                description="Retrieve records from Zoho CRM with optional field filtering and pagination",
                inputSchema=GetRecords.model_json_schema(),
            ),
            types.Tool(
                name=Tools.SEARCH_RECORDS,
                description="Search records using Zoho-style criteria with advanced filtering",
                inputSchema=SearchRecords.model_json_schema(),
            ),
            # Server management tools
            types.Tool(
                name=Tools.HEALTH_CHECK,
                description="Verify server health and Zoho connectivity",
                inputSchema=HealthCheck.model_json_schema(),
            ),
            types.Tool(
                name=Tools.GET_TOOL_INFO,
                description="Return detailed parameter specs for any tool",
                inputSchema=GetToolInfo.model_json_schema(),
            ),
        ]


# Legacy classes kept for backward compatibility
# Note: These are no longer used in the new architecture but kept to avoid breaking imports

class ToolHandlers:
    """Legacy tool handlers class - no longer used but kept for compatibility"""
    
    def __init__(self, zoho_client=None, settings=None):
        """Legacy constructor - no longer functional"""
        pass
    
    async def handle_create_record(self, arguments, access_token):
        """Legacy method - no longer functional"""
        raise NotImplementedError("Tool handlers have been moved to server.py")
    
    async def handle_get_records(self, arguments, access_token):
        """Legacy method - no longer functional"""
        raise NotImplementedError("Tool handlers have been moved to server.py")
    
    async def handle_search_records(self, arguments, access_token):
        """Legacy method - no longer functional"""
        raise NotImplementedError("Tool handlers have been moved to server.py")
    
    async def handle_health_check(self, arguments):
        """Legacy method - no longer functional"""
        raise NotImplementedError("Tool handlers have been moved to server.py")
    
    async def handle_get_tool_info(self, arguments):
        """Legacy method - no longer functional"""
        raise NotImplementedError("Tool handlers have been moved to server.py")
