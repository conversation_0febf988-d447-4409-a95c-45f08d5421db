"""
Enum constants for Zoho CRM MCP server tools
"""

from enum import Enum


class Tools(str, Enum):
    """Tool names for Zoho CRM MCP server"""

    # CRM record management tools
    CREATE_RECORD = "create_record"
    GET_RECORDS = "get_records"
    SEARCH_RECORDS = "search_records"
    
    # Server management tools
    HEALTH_CHECK = "health_check"
    GET_TOOL_INFO = "get_tool_info"


class ZohoModules(str, Enum):
    """Zoho CRM module names"""
    
    LEADS = "Leads"
    CONTACTS = "Contacts"
    ACCOUNTS = "Accounts"
    DEALS = "Deals"
    TASKS = "Tasks"
    EVENTS = "Events"
    CALLS = "Calls"
