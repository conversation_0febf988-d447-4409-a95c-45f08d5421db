"""
OpenAI function calling schemas for Zoho CRM tools compatible with LiveKit agents
"""

from typing import Dict, Any

# OpenAI function calling schemas for LiveKit compatibility
CREATE_RECORD_SCHEMA = {
    "type": "function",
    "function": {
        "name": "create_record",
        "description": "Create a new record in Zoho CRM",
        "parameters": {
            "type": "object",
            "properties": {
                "module": {
                    "type": "string",
                    "description": "Target Zoho CRM module",
                    "enum": [
                        "Leads",
                        "Contacts",
                        "Accounts",
                        "Deals",
                        "Tasks",
                        "Events",
                        "Calls",
                    ],
                },
                "record_data": {
                    "type": "object",
                    "description": "Field/value pairs for the new record. Required fields by module: Leads (Last_Name, Company), Contacts (Last_Name), Accounts (Account_Name), Deals (Deal_Name, Stage, Closing_Date)",
                    "additionalProperties": True,
                },
            },
            "required": ["module", "record_data"],
        },
    },
}

GET_RECORDS_SCHEMA = {
    "type": "function",
    "function": {
        "name": "get_records",
        "description": "Retrieve records from Zoho CRM",
        "parameters": {
            "type": "object",
            "properties": {
                "module": {
                    "type": "string",
                    "description": "Target Zoho CRM module",
                    "enum": [
                        "Leads",
                        "Contacts",
                        "Accounts",
                        "Deals",
                        "Tasks",
                        "Events",
                        "Calls",
                    ],
                },
                "fields": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Array of field names to retrieve (if not provided, returns all fields)",
                },
                "page": {
                    "type": "integer",
                    "description": "Page number (minimum: 1)",
                    "minimum": 1,
                    "default": 1,
                },
                "per_page": {
                    "type": "integer",
                    "description": "Records per page (minimum: 1, maximum: 200)",
                    "minimum": 1,
                    "maximum": 200,
                    "default": 50,
                },
            },
            "required": ["module"],
        },
    },
}

SEARCH_RECORDS_SCHEMA = {
    "type": "function",
    "function": {
        "name": "search_records",
        "description": "Search for records in Zoho CRM using criteria",
        "parameters": {
            "type": "object",
            "properties": {
                "module": {
                    "type": "string",
                    "description": "Target Zoho CRM module",
                    "enum": [
                        "Leads",
                        "Contacts",
                        "Accounts",
                        "Deals",
                        "Tasks",
                        "Events",
                        "Calls",
                    ],
                },
                "search_criteria": {
                    "type": "string",
                    "description": "Search criteria in Zoho format: (Field_Name:operator:value). Examples: (Last_Name:contains:Smith), ((Last_Name:contains:Smith)and(Lead_Source:equals:Website))",
                    "minLength": 3,
                },
                "page": {
                    "type": "integer",
                    "description": "Page number (minimum: 1)",
                    "minimum": 1,
                    "default": 1,
                },
                "per_page": {
                    "type": "integer",
                    "description": "Records per page (minimum: 1, maximum: 200)",
                    "minimum": 1,
                    "maximum": 200,
                    "default": 200,
                },
            },
            "required": ["module", "search_criteria"],
        },
    },
}

HEALTH_CHECK_SCHEMA = {
    "type": "function",
    "function": {
        "name": "health_check",
        "description": "Check the health status of the Zoho CRM connection",
        "parameters": {"type": "object", "properties": {}, "required": []},
    },
}

GET_TOOL_INFO_SCHEMA = {
    "type": "function",
    "function": {
        "name": "get_tool_info",
        "description": "Get information about available tools",
        "parameters": {
            "type": "object",
            "properties": {
                "tool_name": {
                    "type": "string",
                    "description": "Specific tool name to get information for",
                    "enum": [
                        "create_record",
                        "get_records",
                        "search_records",
                        "health_check",
                        "get_tool_info",
                    ],
                }
            },
            "required": [],
        },
    },
}

# Dictionary mapping tool names to their OpenAI schemas
OPENAI_TOOL_SCHEMAS: Dict[str, Dict[str, Any]] = {
    "create_record": CREATE_RECORD_SCHEMA,
    "get_records": GET_RECORDS_SCHEMA,
    "search_records": SEARCH_RECORDS_SCHEMA,
    "health_check": HEALTH_CHECK_SCHEMA,
    "get_tool_info": GET_TOOL_INFO_SCHEMA,
}
