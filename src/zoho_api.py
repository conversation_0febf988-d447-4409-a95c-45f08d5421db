#!/usr/bin/env python3
"""
Zoho CRM API client module
Handles all Zoho CRM REST API interactions with proper error handling and retry logic.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone

import httpx
from .config import ZohoConfig

logger = logging.getLogger(__name__)


class ZohoAPIError(Exception):
    """Custom exception for Zoho API errors"""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data or {}
        super().__init__(self.message)


class ZohoAPIClient:
    """Zoho CRM API client with proper error handling and retry logic"""

    def __init__(self, config: ZohoConfig):
        self.config = config
        self.base_url = config.api_base_url
        self.timeout = config.timeout
        self.max_retries = config.max_retries

        # HTTP client configuration
        self.client_config = {
            "timeout": httpx.Timeout(self.timeout),
            "limits": httpx.Limits(max_keepalive_connections=10, max_connections=20),
            "follow_redirects": True,
        }

        logger.info(f"Zoho API client initialized - Base URL: {self.base_url}")

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        access_token: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        retry_count: int = 0,
    ) -> Dict[str, Any]:
        """Make HTTP request to Zoho API with retry logic"""

        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {
            "Authorization": f"Zoho-oauthtoken {access_token}",
            "Content-Type": "application/json",
            "User-Agent": f"Zoho-CRM-MCP-Server/{self.config.api_version}",
        }

        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                logger.debug(f"Making {method} request to {url}")

                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(
                        url, headers=headers, json=data, params=params
                    )
                elif method.upper() == "PUT":
                    response = await client.put(
                        url, headers=headers, json=data, params=params
                    )
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers, params=params)
                else:
                    raise ZohoAPIError(f"Unsupported HTTP method: {method}")

                return await self._handle_response(
                    response, method, endpoint, access_token, data, params, retry_count
                )

        except httpx.TimeoutException:
            logger.warning(f"Request timeout for {method} {url}")
            if retry_count < self.max_retries:
                await asyncio.sleep(2**retry_count)  # Exponential backoff
                return await self._make_request(
                    method, endpoint, access_token, data, params, retry_count + 1
                )
            raise ZohoAPIError(f"Request timeout after {self.max_retries} retries")

        except httpx.RequestError as e:
            logger.error(f"Request error for {method} {url}: {e}")
            if retry_count < self.max_retries:
                await asyncio.sleep(2**retry_count)
                return await self._make_request(
                    method, endpoint, access_token, data, params, retry_count + 1
                )
            raise ZohoAPIError(f"Request failed: {str(e)}")

    async def _handle_response(
        self,
        response: httpx.Response,
        method: str,
        endpoint: str,
        access_token: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        retry_count: int = 0,
    ) -> Dict[str, Any]:
        """Handle API response with proper error handling"""

        try:
            response_data = response.json()
        except json.JSONDecodeError:
            response_data = {"raw_response": response.text}

        # Success responses
        if response.status_code in [200, 201, 202]:
            logger.debug(f"Successful {method} request to {endpoint}")
            return {
                "success": True,
                "data": response_data,
                "status_code": response.status_code,
            }

        # Handle specific error cases
        if response.status_code == 401:
            logger.error("Authentication failed - invalid or expired token")
            return {
                "success": False,
                "error": "AUTHENTICATION_FAILED",
                "message": "Invalid or expired access token",
                "status_code": 401,
                "details": response_data,
            }

        elif response.status_code == 403:
            logger.error("Access forbidden - insufficient permissions")
            return {
                "success": False,
                "error": "ACCESS_FORBIDDEN",
                "message": "Insufficient permissions for this operation",
                "status_code": 403,
                "details": response_data,
            }

        elif response.status_code == 404:
            logger.warning(f"Resource not found: {endpoint}")
            return {
                "success": False,
                "error": "RESOURCE_NOT_FOUND",
                "message": "Requested resource not found",
                "status_code": 404,
                "details": response_data,
            }

        elif response.status_code == 429:
            logger.warning("Rate limit exceeded")
            if retry_count < self.max_retries:
                # Wait longer for rate limits
                await asyncio.sleep(60)  # Wait 1 minute for rate limit
                return await self._make_request(
                    method, endpoint, access_token, data, params, retry_count + 1
                )

            return {
                "success": False,
                "error": "RATE_LIMIT_EXCEEDED",
                "message": "API rate limit exceeded",
                "status_code": 429,
                "details": response_data,
            }

        elif response.status_code >= 500:
            logger.error(f"Server error {response.status_code}")
            if retry_count < self.max_retries:
                await asyncio.sleep(2**retry_count)
                return await self._make_request(
                    method, endpoint, access_token, data, params, retry_count + 1
                )

            return {
                "success": False,
                "error": "SERVER_ERROR",
                "message": f"Zoho server error: {response.status_code}",
                "status_code": response.status_code,
                "details": response_data,
            }

        # Other client errors
        else:
            logger.error(f"API error {response.status_code}: {response_data}")
            return {
                "success": False,
                "error": f"HTTP_{response.status_code}",
                "message": f"API request failed: {response.reason_phrase}",
                "status_code": response.status_code,
                "details": response_data,
            }

    async def create_record(
        self, module: str, record_data: Dict[str, Any], access_token: str
    ) -> Dict[str, Any]:
        """Create a new record in the specified module"""
        endpoint = f"{module}"
        payload = {"data": [record_data]}

        logger.info(f"Creating {module} record")
        return await self._make_request("POST", endpoint, access_token, data=payload)

    async def get_records(
        self,
        module: str,
        access_token: str,
        fields: Optional[List[str]] = None,
        page: int = 1,
        per_page: int = 50,
    ) -> Dict[str, Any]:
        """Get records from the specified module with pagination"""
        endpoint = f"{module}"
        params = {"page": page, "per_page": min(per_page, 200)}  # Zoho max is 200

        if fields:
            params["fields"] = ",".join(fields)

        logger.info(f"Getting {module} records (page {page}, per_page {per_page})")
        return await self._make_request("GET", endpoint, access_token, params=params)

    async def search_records(
        self,
        module: str,
        search_criteria: str,
        access_token: str,
        page: int = 1,
        per_page: int = 200,
    ) -> Dict[str, Any]:
        """Search records using criteria"""
        endpoint = f"{module}/search"
        params = {
            "criteria": search_criteria,
            "page": page,
            "per_page": min(per_page, 200),
        }

        logger.info(f"Searching {module} records with criteria: {search_criteria}")
        return await self._make_request("GET", endpoint, access_token, params=params)

    async def get_record(
        self, module: str, record_id: str, access_token: str
    ) -> Dict[str, Any]:
        """Get a specific record by ID"""
        endpoint = f"{module}/{record_id}"

        logger.info(f"Getting {module} record {record_id}")
        return await self._make_request("GET", endpoint, access_token)

    async def update_record(
        self,
        module: str,
        record_id: str,
        record_data: Dict[str, Any],
        access_token: str,
    ) -> Dict[str, Any]:
        """Update an existing record"""
        endpoint = f"{module}/{record_id}"
        payload = {"data": [record_data]}

        logger.info(f"Updating {module} record {record_id}")
        return await self._make_request("PUT", endpoint, access_token, data=payload)

    async def delete_record(
        self, module: str, record_id: str, access_token: str
    ) -> Dict[str, Any]:
        """Delete a record"""
        endpoint = f"{module}/{record_id}"

        logger.info(f"Deleting {module} record {record_id}")
        return await self._make_request("DELETE", endpoint, access_token)

    def get_health_info(self) -> Dict[str, Any]:
        """Get API client health information"""
        return {
            "base_url": self.base_url,
            "environment": self.config.environment,
            "api_version": self.config.api_version,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "status": "healthy",
        }
