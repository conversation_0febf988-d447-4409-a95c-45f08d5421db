#!/usr/bin/env python3
"""
Zoho CRM MCP Server - Production Ready
A streamable HTTP Model Context Protocol server for Zoho CRM REST API integration.
Modular, configurable, and production-ready implementation.
"""

import asyncio
import logging
import sys
from typing import Optional

import uvicorn

try:
    # When running as module: python -m src.main
    from .config import get_settings, Settings
    from .server import ZohoCRMMCPServer
except ImportError:
    # When running directly: cd src && python main.py
    from config import get_settings, Settings
    from server import ZohoCRMMCPServer


# Configure logging
def setup_logging(settings: Settings) -> None:
    """Setup logging configuration"""
    log_level = getattr(logging, settings.server.log_level.upper())

    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)],
    )

    # Set specific logger levels
    if settings.server.debug:
        logging.getLogger("httpx").setLevel(logging.DEBUG)
        logging.getLogger("mcp").setLevel(logging.DEBUG)
    else:
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)


def validate_settings(settings: Settings) -> None:
    """Validate required settings and configuration"""
    logger = logging.getLogger(__name__)

    # # Check for missing required settings
    # missing = settings.validate_required_settings()
    # if missing:
    #     logger.error("Missing required configuration:")
    #     for key, description in missing.items():
    #         logger.error(f"  {key}: {description}")
    #     sys.exit(1)

    # Log configuration summary
    logger.info("🚀 Starting Zoho CRM MCP Server")
    logger.info(f"📍 Server: {settings.server.name} v{settings.server.version}")
    logger.info(f"🌐 Host: {settings.server.host}:{settings.server.port}")
    logger.info(f"📍 Zoho Environment: {settings.zoho.environment}")
    logger.info(f"🌐 Zoho API Base URL: {settings.zoho.api_base_url}")
    logger.info(f"🔐 Authentication Required: {settings.security.require_auth}")

    if settings.security.require_auth:
        logger.info(
            f"💡 Authentication: {settings.security.auth_header_name} header with {settings.security.auth_scheme} scheme"
        )

    if settings.server.debug:
        logger.warning("⚠️  Debug mode is enabled - not recommended for production")


def create_app():
    """Create and configure the application"""
    logger = logging.getLogger(__name__)

    try:
        # Load settings
        settings = get_settings()

        # Setup logging
        setup_logging(settings)

        # Validate configuration
        validate_settings(settings)

        # Create MCP server
        mcp_server = ZohoCRMMCPServer(settings)

        # Create ASGI app
        app = mcp_server.create_app()

        # Log server info
        server_info = mcp_server.get_server_info()
        logger.info(f"🔧 Available tools: {', '.join(server_info['available_tools'])}")
        logger.info("✅ Server initialization complete")

        return app

    except Exception as e:
        logger.error(f"❌ Failed to create application: {e}")
        sys.exit(1)


def run_server():
    """Run the server with uvicorn"""
    logger = logging.getLogger(__name__)

    try:
        # Load settings for server configuration
        settings = get_settings()
        setup_logging(settings)

        logger.info("🌐 Starting streamable HTTP server")
        logger.info(f"🔗 MCP Endpoint: {settings.get_mcp_url()}")
        logger.info(
            "🔗 Use this server with MCP clients that support streamable HTTP transport"
        )

        # Run with uvicorn
        uvicorn.run(
            "src.main:create_app",
            factory=True,
            host=settings.server.host,
            port=settings.server.port,
            log_level=settings.server.log_level.lower(),
            reload=settings.server.debug,
            access_log=settings.server.debug,
        )

    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run_server()
