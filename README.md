# Zoho CRM MCP Server

A production-ready streamable HTTP Model Context Protocol (MCP) server for Zoho CRM integration. Clean, modular implementation using standard MCP library with direct REST API calls.

## ✨ Features

- **🪶 Lightweight**: No heavy SDK dependencies - direct REST API calls
- **🚀 Fast**: Streamable HTTP transport with async operations
- **🏗️ Modular**: Clean separation of concerns with dedicated modules
- **🔧 Production-Ready**: Environment-based configuration and comprehensive error handling
- **🌍 Multi-region**: Supports US, EU, IN, AU, JP, CA, CN data centers
- **🔐 Bearer Token Auth**: Secure authentication via Authorization headers
- **📊 Module Support**: Works with all Zoho CRM modules (Leads, Contacts, Accounts, etc.)
- **🔍 Advanced Search**: Powerful search capabilities with custom criteria
- **📖 Enhanced Documentation**: Comprehensive tool documentation with parameter details

## 📁 Project Structure

```
zoho-mcp/
├── src/
│   ├── __init__.py          # Package initialization
│   ├── main.py              # Main entry point (production-ready)
│   ├── config.py            # Configuration management with Pydantic
│   ├── auth.py              # Authentication and token handling
│   ├── zoho_api.py          # Zoho CRM API client with retry logic
│   ├── tools.py             # MCP tool definitions and handlers
│   └── server.py            # MCP server implementation
├── requirements.txt         # Production dependencies
├── .env.example            # Comprehensive environment template
├── test_modular_server.py   # Server testing utilities
└── README.md               # This documentation
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Zoho CRM account with API access
- Zoho OAuth app credentials

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd zoho-mcp

# Install dependencies
pip install -r requirements.txt
```

### Configuration

1. **Create `.env` file from template:**

```bash
cp .env.example .env
```

2. **Configure your environment variables:**

```env
# Server Configuration
SERVER_HOST=127.0.0.1
SERVER_PORT=8000
SERVER_NAME=Zoho CRM MCP Server

# Zoho Configuration
ZOHO_ENVIRONMENT=US

# Security Configuration
AUTH_REQUIRED=true
AUTH_HEADER_NAME=Authorization
AUTH_HEADER_FORMAT=Bearer {token}

# CORS Configuration
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*

# Logging
LOG_LEVEL=INFO
ZOHO_CLIENT_SECRET=your_client_secret
ZOHO_ENVIRONMENT=US
ZOHO_ACCESS_TOKEN=your_access_token
```

2. **Get OAuth Access Token:**

You can obtain an access token through Zoho's OAuth flow. The server supports:

- Authorization header: `Bearer <token>`
- Environment variable: `ZOHO_ACCESS_TOKEN`

### Running the Server

```bash
# Start the server (production-ready modular version)
python -m src.main

# Alternative: Run from src directory
cd src && python main.py

# For development with auto-reload
uvicorn src.main:create_app --factory --reload --host 127.0.0.1 --port 8000
```

The server will start on `http://127.0.0.1:8000` by default.

### Testing the Server

```bash
# Test the modular server functionality
python test_modular_server.py
```

**Note**: The test script requires a proper MCP client connection. Direct HTTP requests will return "Missing session ID" which is expected behavior for the MCP protocol.

## 🏗️ Modular Architecture

The server is built with a clean, modular architecture:

### Core Modules

- **`config.py`**: Environment-based configuration management using Pydantic
- **`auth.py`**: Bearer token authentication and context management
- **`zoho_api.py`**: Zoho CRM API client with retry logic and error handling
- **`tools.py`**: MCP tool definitions with comprehensive documentation
- **`server.py`**: MCP server implementation with ASGI integration
- **`main.py`**: Production-ready entry point with logging and lifecycle management

### Key Benefits

- **Separation of Concerns**: Each module has a single responsibility
- **Easy Testing**: Modular design enables unit testing of individual components
- **Maintainability**: Clear structure makes the codebase easy to understand and modify
- **Extensibility**: New tools and features can be added without affecting existing code
- **Production Ready**: Comprehensive error handling, logging, and configuration management

## 🛠️ Available Tools

### `create_record`

Create a new record in Zoho CRM.

```json
{
  "module": "Leads",
  "record_data": {
    "Last_Name": "Smith",
    "Company": "ABC Corp",
    "Email": "<EMAIL>"
  }
}
```

### `get_records`

Retrieve records from a module.

```json
{
  "module": "Leads",
  "fields": ["Last_Name", "Company", "Email"],
  "page": 1,
  "per_page": 50
}
```

### `search_records`

Search records with custom criteria.

```json
{
  "module": "Leads",
  "search_criteria": "(Email:equals:<EMAIL>)"
}
```

### `health_check`

Check server health and configuration.

```json
{}
```

### `get_tool_info`

Get detailed information about available tools and their parameters.

**Parameters:**

- `tool_name` (optional): Name of specific tool to get info for

```json
{
  "tool_name": "create_record"
}
```

**Response includes:**

- Tool description and purpose
- Required and optional parameters
- Parameter types and validation rules
- Usage examples
- Module-specific requirements

## 🧪 Testing

The project includes comprehensive test coverage with 28 test cases covering all functionality:

### Test Structure

- **MCP Tools Tests** (`tests/test_tools.py`): 10 tests covering all MCP tools and resources
- **Zoho Service Tests** (`tests/test_zoho.py`): 18 tests covering Zoho SDK integration
- **Test Configuration** (`tests/conftest.py`): Shared fixtures and utilities
- **Pytest Configuration** (`pytest.ini`): Async testing and discovery settings

### Running Tests

```bash
# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Run all tests (28 tests)
python -m pytest tests/ -v

# Run specific test suites
python -m pytest tests/test_tools.py -v    # MCP tools tests (10 tests)
python -m pytest tests/test_zoho.py -v     # Zoho service tests (18 tests)

# Run with detailed output
python -m pytest tests/ -v --tb=short

# Run with coverage report
python -m pytest tests/ --cov=src --cov-report=html
```

### Test Results

Current test status: **28/28 tests passing (100% success rate)**

```
tests/test_tools.py::TestMCPTools::test_create_record_tool_success PASSED
tests/test_tools.py::TestMCPTools::test_create_record_tool_error PASSED
tests/test_tools.py::TestMCPTools::test_fetch_records_tool_success PASSED
tests/test_tools.py::TestMCPTools::test_fetch_records_tool_empty_result PASSED
tests/test_tools.py::TestMCPTools::test_search_records_tool_success PASSED
tests/test_tools.py::TestMCPTools::test_search_records_tool_error PASSED
tests/test_tools.py::TestMCPTools::test_get_module_schema_resource_success PASSED
tests/test_tools.py::TestMCPTools::test_get_module_fields_resource_success PASSED
tests/test_tools.py::TestMCPTools::test_get_picklist_values_resource_success PASSED
tests/test_tools.py::TestMCPTools::test_resource_error_handling PASSED
tests/test_zoho.py::TestZohoCRMService::test_zoho_service_initialization_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_zoho_service_initialization_failure PASSED
tests/test_zoho.py::TestZohoCRMService::test_is_authenticated_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_is_authenticated_failure PASSED
tests/test_zoho.py::TestZohoCRMService::test_refresh_authentication_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_create_record_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_create_record_error PASSED
tests/test_zoho.py::TestZohoCRMService::test_fetch_records_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_fetch_records_empty PASSED
tests/test_zoho.py::TestZohoCRMService::test_search_records_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_search_records_module_error PASSED
tests/test_zoho.py::TestZohoCRMService::test_get_module_schema_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_get_module_fields_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_get_picklist_values_success PASSED
tests/test_zoho.py::TestZohoCRMService::test_get_picklist_values_no_values PASSED
tests/test_zoho.py::TestZohoCRMService::test_service_method_exception_handling PASSED
tests/test_zoho.py::TestZohoCRMService::test_environment_mapping_basic PASSED
tests/test_zoho.py::TestZohoCRMService::test_service_has_required_methods PASSED
```

### Development Testing

```bash
# Watch mode for development
python -m pytest tests/ --tb=short -x  # Stop on first failure

# Test specific functionality
python -m pytest tests/test_tools.py::TestMCPTools::test_create_record_tool_success -v

# Debug failing tests
python -m pytest tests/ --pdb  # Drop into debugger on failure
```

## 🔧 Development

### Code Quality

```bash
# Format code
black src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

### Development Workflow

1. Make changes to source code
2. Run tests to ensure functionality: `python -m pytest tests/ -v`
3. Check code quality with linting and formatting
4. Test the server manually if needed
5. Commit changes with descriptive messages

## 📋 Implementation Status

### ✅ Phase 1: Complete Implementation

**Foundation & Core Infrastructure:**

- [x] Project structure setup with proper organization
- [x] Dependencies configuration (FastMCP, Zoho SDK, testing)
- [x] Configuration management with Pydantic settings
- [x] Environment template and secure credential handling
- [x] Comprehensive test structure with 28 test cases

**FastMCP Server Implementation:**

- [x] FastMCP server with HTTP streamable transport
- [x] Server configuration and middleware setup
- [x] Health check and status endpoints
- [x] Multi-transport support (HTTP/STDIO)

**Zoho SDK Integration:**

- [x] Zoho CRM Python SDK v8.0 integration
- [x] OAuth 2.0 authentication with automatic token refresh
- [x] Multi-environment support (US, EU, IN, CN, AU)
- [x] Connection management and error handling
- [x] Demo mode for development without credentials

**MCP Tools Implementation:**

- [x] `create_record` - Create new records in any Zoho CRM module
- [x] `fetch_records` - Retrieve records with advanced filtering and pagination
- [x] `search_records` - Search across multiple modules with complex criteria

**MCP Resources Implementation:**

- [x] `zoho://modules/{module}/schema` - Get complete module schema
- [x] `zoho://modules/{module}/fields` - Get field metadata and validation rules
- [x] `zoho://modules/{module}/picklists/{field}` - Get picklist values for fields

**Testing & Quality Assurance:**

- [x] Comprehensive test coverage (28/28 tests passing)
- [x] MCP tools testing with success/error scenarios
- [x] Zoho service testing with mocked SDK responses
- [x] Async testing patterns with pytest-asyncio
- [x] Error handling and edge case testing

### 🎯 Current Capabilities

## 🛠️ Available Tools

### MCP Tools

- **`create_record`** - Create new records in any Zoho CRM module
  - Supports all standard and custom modules
  - Duplicate checking and validation
  - Comprehensive error handling
- **`fetch_records`** - Retrieve records with advanced filtering and pagination
  - Field selection and filtering
  - Pagination support with configurable page sizes
  - Sorting and ordering options
- **`search_records`** - Search across multiple modules with complex criteria
  - Multi-module search capabilities
  - Text-based search with relevance scoring
  - Configurable result limits

### MCP Resources

- **`zoho://modules/{module}/schema`** - Get complete module schema
  - Module metadata and permissions
  - Field definitions and relationships
  - API capabilities and limitations
- **`zoho://modules/{module}/fields`** - Get field metadata and validation rules
  - Field types and constraints
  - Validation rules and requirements
  - Custom field information
- **`zoho://modules/{module}/picklists/{field}`** - Get picklist values for fields
  - Available options for dropdown fields
  - Display and actual values
  - Hierarchical picklist support

### 🚀 Future Enhancements (Phase 2)

- [ ] `update_record` - Update existing records with validation
- [ ] `delete_record` - Delete records (soft delete when possible)
- [ ] Advanced filtering and query capabilities
- [ ] Bulk operations for high-volume data processing
- [ ] Webhook integration for real-time updates
- [ ] Rate limiting and performance optimization

## 🔒 Security

- OAuth 2.0 authentication with automatic token refresh
- Secure credential storage using environment variables
- Rate limiting to prevent API abuse
- Input validation and sanitization
- No persistent storage of sensitive data

## 📊 Performance

- **Target Response Time**: < 2 seconds for 95% of requests
- **Resource Load Time**: < 1 second for metadata
- **Concurrent Support**: 50+ simultaneous MCP clients
- **Memory Usage**: < 100MB base footprint

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Check the [Issues](../../issues) page
- Review the [Documentation](docs/)
- Contact the development team

## 🚀 Quick Validation

To verify your installation is working correctly:

```bash
# 1. Activate virtual environment
source venv/bin/activate

# 2. Run all tests
python -m pytest tests/ -v

# 3. Start the server
python src/main.py

# 4. Check server health (in another terminal)
curl http://localhost:8080/mcp/health
```

Expected output:

- All 28 tests should pass
- Server should start without errors
- Health check should return server status

## 🔍 Troubleshooting

### Common Issues

**Tests Failing:**

```bash
# Check Python version (requires 3.11+)
python --version

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Run tests with verbose output
python -m pytest tests/ -v --tb=long
```

**Server Won't Start:**

```bash
# Check .env file exists and has required variables
cat .env

# Start in demo mode (without Zoho credentials)
# Server will use mock data for testing
python src/main.py
```

**Zoho Authentication Issues:**

- **"MANDATORY VALUE ERROR - Value missing for refresh_token"**: You need to complete the OAuth flow first

  ```bash
  python oauth_helper.py --get-auth-url
  # Visit URL, grant permissions, then:
  python oauth_helper.py --exchange-code YOUR_CODE
  ```

- **"OAUTH_SCOPE_MISMATCH" Error**: Your OAuth token doesn't have required permissions

  ```bash
  # Diagnose scope issues
  python oauth_helper.py --diagnose-scopes YOUR_REFRESH_TOKEN

  # Generate new token with all required scopes
  python oauth_helper.py --get-auth-url
  python oauth_helper.py --exchange-code YOUR_NEW_CODE
  ```

  📖 **See [OAUTH_SCOPE_TROUBLESHOOTING.md](OAUTH_SCOPE_TROUBLESHOOTING.md) for detailed scope troubleshooting**

- Verify your Zoho credentials in `.env`
- Check that your refresh token is still valid
- Ensure your Zoho app has the required scopes
- Server will automatically fall back to demo mode if authentication fails
- Use `python oauth_helper.py --test-token YOUR_TOKEN` to validate tokens

---

## 🚀 Production Deployment

### Environment Variables

For production deployment, configure these essential environment variables:

```env
# Production settings
DEBUG=false
LOG_LEVEL=INFO
AUTH_REQUIRED=true

# Server configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
WORKERS=4

# Security
CORS_ALLOWED_ORIGINS=https://yourdomain.com
CORS_ALLOW_CREDENTIALS=true

# Performance
MAX_CONNECTIONS=1000
REQUEST_TIMEOUT=30
KEEP_ALIVE_TIMEOUT=5

# Monitoring
HEALTH_CHECK_PATH=/health
METRICS_ENABLED=true
```

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY .env .

EXPOSE 8000
CMD ["python", "src/main.py"]
```

### Load Balancer Health Check

The server provides a health check endpoint at `/health` for load balancer monitoring.

### Security Considerations

1. **Never store access tokens in environment variables in production**
2. **Use HTTPS termination at load balancer level**
3. **Configure appropriate CORS origins**
4. **Enable rate limiting for production traffic**
5. **Monitor authentication failures and implement alerting**

## 📝 License

MIT License - see LICENSE file for details.

---

**Status**: Production-Ready ✅ - Modular MCP server with comprehensive Zoho CRM integration, environment-based configuration, and deployment-ready architecture.
