import requests
import json
import urllib.parse
import webbrowser
from typing import Dict, Optional


class ZohoOAuthGenerator:
    def __init__(
        self,
        client_id: str,
        client_secret: str,
        redirect_uri: str = "https://localhost",
    ):
        """
        Initialize the Zoho OAuth generator.

        Args:
            client_id: Your Zoho CRM client ID
            client_secret: Your Zoho CRM client secret
            redirect_uri: Redirect URI (must match what's configured in Zoho)
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.base_auth_url = "https://accounts.zoho.com/oauth/v2/auth"
        self.token_url = "https://accounts.zoho.com/oauth/v2/token"

    def get_authorization_url(
        self, scopes: str = "ZohoCRM.modules.ALL,ZohoCRM.users.READ,ZohoCRM.org.READ"
    ) -> str:
        """
        Generate the authorization URL for the user to visit.

        Args:
            scopes: Comma-separated list of scopes (default includes modules, users, and org access)

        Returns:
            Authorization URL string
        """
        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "scope": scopes,
            "redirect_uri": self.redirect_uri,
            "access_type": "offline",
        }

        auth_url = f"{self.base_auth_url}?{urllib.parse.urlencode(params)}"
        return auth_url

    def exchange_code_for_token(self, authorization_code: str) -> Dict:
        """
        Exchange authorization code for access token.

        Args:
            authorization_code: The code received from the authorization callback

        Returns:
            Dictionary containing token information
        """
        data = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "redirect_uri": self.redirect_uri,
            "code": authorization_code,
        }

        response = requests.post(self.token_url, data=data)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(
                f"Token exchange failed: {response.status_code} - {response.text}"
            )

    def refresh_access_token(self, refresh_token: str) -> Dict:
        """
        Refresh the access token using refresh token.

        Args:
            refresh_token: The refresh token from previous authentication

        Returns:
            Dictionary containing new token information
        """
        data = {
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
        }

        response = requests.post(self.token_url, data=data)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(
                f"Token refresh failed: {response.status_code} - {response.text}"
            )

    def generate_access_token_interactive(
        self, scopes: str = "ZohoCRM.modules.ALL,ZohoCRM.users.READ,ZohoCRM.org.READ"
    ) -> Dict:
        """
        Interactive method to generate access token (opens browser).

        Args:
            scopes: Comma-separated list of scopes

        Returns:
            Dictionary containing token information
        """
        # Step 1: Get authorization URL
        auth_url = self.get_authorization_url(scopes)

        print("Step 1: Opening browser for authorization...")
        print(f"If browser doesn't open, visit this URL: {auth_url}")

        # Open browser
        webbrowser.open(auth_url)

        # Step 2: Get authorization code from user
        print("\nStep 2: After authorizing, you'll be redirected to a URL.")
        print("Copy the 'code' parameter from the redirect URL.")

        auth_code = input("Enter the authorization code: ").strip()

        # Step 3: Exchange code for token
        print("\nStep 3: Exchanging code for access token...")
        token_data = self.exchange_code_for_token(auth_code)

        return token_data


def get_required_scopes():
    """
    Get required scopes based on user needs.

    Returns:
        String of comma-separated scopes
    """
    print("\nSelect the scopes you need:")
    print("1. Full CRM Access (Recommended)")
    print("2. Read-only Access")
    print("3. Custom scopes")
    print("4. Minimal scopes (for testing)")

    choice = input("Enter your choice (1-4): ").strip()

    if choice == "1":
        return "ZohoCRM.modules.ALL,ZohoCRM.users.READ,ZohoCRM.org.READ,ZohoCRM.settings.READ"
    elif choice == "2":
        return "ZohoCRM.modules.READ,ZohoCRM.users.READ,ZohoCRM.org.READ"
    elif choice == "3":
        print("\nAvailable scopes:")
        print("- ZohoCRM.modules.ALL (Full module access)")
        print("- ZohoCRM.modules.READ (Read-only module access)")
        print("- ZohoCRM.modules.CREATE (Create records)")
        print("- ZohoCRM.modules.UPDATE (Update records)")
        print("- ZohoCRM.modules.DELETE (Delete records)")
        print("- ZohoCRM.users.READ (Read user info)")
        print("- ZohoCRM.users.ALL (Full user access)")
        print("- ZohoCRM.org.READ (Read organization info)")
        print("- ZohoCRM.settings.READ (Read settings)")
        print("- ZohoCRM.settings.ALL (Full settings access)")

        custom_scopes = input("\nEnter comma-separated scopes: ").strip()
        return custom_scopes
    else:
        return "ZohoCRM.modules.READ,ZohoCRM.users.READ,ZohoCRM.org.READ"


def main():
    """
    Main function to demonstrate usage.
    """
    # Configuration
    CLIENT_ID = input("Enter your Zoho CRM Client ID: ").strip()
    CLIENT_SECRET = input("Enter your Zoho CRM Client Secret: ").strip()
    REDIRECT_URI = (
        input("Enter your Redirect URI (or press Enter for default): ").strip()
        or "https://localhost"
    )

    # Get required scopes
    scopes = get_required_scopes()

    # Initialize OAuth generator
    oauth_gen = ZohoOAuthGenerator(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI)

    try:
        # Generate access token
        token_data = oauth_gen.generate_access_token_interactive(scopes)

        print("\n" + "=" * 50)
        print("SUCCESS! Your OAuth tokens:")
        print("=" * 50)
        print(f"Access Token: {token_data.get('access_token')}")
        print(f"Refresh Token: {token_data.get('refresh_token')}")
        print(f"Token Type: {token_data.get('token_type')}")
        print(f"Expires In: {token_data.get('expires_in')} seconds")
        print(f"Scopes: {scopes}")
        print("=" * 50)

        # Save tokens to file with scope information
        token_data["scopes"] = scopes
        with open("zoho_tokens.json", "w") as f:
            json.dump(token_data, f, indent=2)
        print("\nTokens saved to 'zoho_tokens.json'")

        # Example of how to use the access token
        print("\nExample usage in API calls:")
        print("headers = {")
        print(
            f"    'Authorization': 'Zoho-oauthtoken {token_data.get('access_token')}'"
        )
        print("}")

    except Exception as e:
        print(f"Error: {e}")


def refresh_token_example():
    """
    Example function showing how to refresh an existing token.
    """
    CLIENT_ID = "your_client_id"
    CLIENT_SECRET = "your_client_secret"

    # Load existing tokens
    try:
        with open("zoho_tokens.json", "r") as f:
            tokens = json.load(f)

        refresh_token = tokens.get("refresh_token")

        oauth_gen = ZohoOAuthGenerator(CLIENT_ID, CLIENT_SECRET)
        new_tokens = oauth_gen.refresh_access_token(refresh_token)

        print("New access token:", new_tokens.get("access_token"))

    except FileNotFoundError:
        print("No existing tokens found. Run the main function first.")
    except Exception as e:
        print(f"Error refreshing token: {e}")


if __name__ == "__main__":
    main()

    # Uncomment the line below to test token refresh
    # refresh_token_example()
