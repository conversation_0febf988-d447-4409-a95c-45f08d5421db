# Cal.com MCP Server (Standard MCP Implementation)

A professional MCP (Model Context Protocol) server for Cal.com, built with the standard MCP framework. This server provides comprehensive tools for AI agents to manage Cal.com bookings with proper JSON schema validation and structured responses.

## Features

- **Standard MCP Protocol**: Built with the official MCP framework (v1.9.4+)
- **Comprehensive Tool Set**: 6 tools covering all booking management needs
- **JSON Schema Validation**: Proper input validation with detailed schemas
- **Structured Responses**: Well-formatted JSON responses with success/error handling
- **Professional Architecture**: Follows MCP best practices and patterns
- **HTTP Session Management**: Streamable HTTP transport with CORS support

## Available Tools

1. **`list_bookings`** - Get all bookings (no parameters needed)
2. **`create_booking`** - Create a new booking (5 required parameters only)
3. **`list_event_types`** - Get available event types (no parameters needed)
4. **`check_availability`** - Check available time slots (3 required parameters only)

## Project Structure

```
cal-com-mcp-server/
├── src/
│   ├── __init__.py
│   ├── server.py              # Main MCP server with standard protocol
│   ├── cal_client.py          # Cal.com API client
│   ├── models.py              # Pydantic models for Cal.com API
│   ├── config.py              # Configuration management
│   └── constants/
│       ├── __init__.py
│       ├── enum.py            # Tool name constants
│       └── schema.py          # Pydantic schemas for tool inputs
├── tests/
│   ├── __init__.py
│   └── test_server.py
├── .env                       # Environment variables
├── requirements.txt           # Dependencies (standard MCP)
├── pyproject.toml            # Modern Python project configuration
├── run_server.py             # Server entry point
├── test_migration.py         # Migration validation script
└── README.md
```

## Prerequisites

- Python 3.9+
- A Cal.com API key ([see how to get one](https://cal.com/docs/api/authentication))

## Setup

1. **Clone the repository**

   ```bash
   git clone <your-repo-url>
   cd cal-com-mcp-server
   ```

2. **Create and activate a virtual environment (recommended)**

   ```bash
   python -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   # Or using the modern approach:
   pip install -e .
   ```

4. **Configure environment variables**

   - Copy `.env.example` to `.env` (or create `.env`):
     ```env
     CALCOM_API_KEY=cal_test_your_api_key_here
     CALCOM_BASE_URL=https://api.cal.com/v2
     MCP_SERVER_NAME=Cal.com MCP Server
     MCP_SERVER_PORT=8000
     MCP_SERVER_HOST=localhost
     LOG_LEVEL=INFO
     ```
   - Replace `CALCOM_API_KEY` with your actual Cal.com API key.

5. **Run the server**
   ```bash
   python run_server.py
   ```
   The server will start on the host and port specified in your `.env` (default: `localhost:8000`).

## Usage

### Health Check

```bash
curl http://localhost:8000/health
```

### List Event Types

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "list_event_types",
      "arguments": {}
    }
  }'
```

### List Bookings

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "list_bookings",
      "arguments": {}
    }
  }'
```

### Check Availability

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "check_availability",
      "arguments": {
        "event_type_id": 123,
        "date": "2024-01-15",
        "timezone": "America/New_York"
      }
    }
  }'
```

### Create Booking

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
      "name": "create_booking",
      "arguments": {
        "event_type_id": 123,
        "start_time": "2024-01-15T14:00:00",
        "attendee_name": "Jane Smith",
        "attendee_email": "<EMAIL>",
        "timezone": "America/New_York"
      }
    }
  }'
```

## AI Agent Integration

This server follows standard MCP protocol for seamless AI agent integration:

### Professional Design

- **JSON Schema Validation**: All tool inputs are validated with comprehensive schemas
- **Structured Responses**: Consistent JSON responses with success/error indicators
- **Standard MCP Protocol**: Compatible with all MCP-compliant AI agents and frameworks
- **Comprehensive Tool Set**: 6 tools covering all booking management scenarios

### Recommended Workflow

1. **List event types**: Use `list_event_types` to discover available meeting types
2. **Check availability**: Use `check_availability` to find open time slots
3. **Create booking**: Use `create_booking` with validated parameters
4. **Verify booking**: Use `list_bookings` to confirm creation

### Error Handling

- Standard MCP error responses with `isError: true`
- Detailed error messages in structured format
- Proper HTTP status codes and JSON-RPC error handling
- Comprehensive logging for debugging

## Tool Reference

### `list_bookings`

- **Parameters**:
  - `limit` (int, optional): Maximum number of bookings to return (default: 100)
  - `status` (string, optional): Filter by booking status
  - `days_back` (int, optional): Number of days back to search (default: 30)
  - `days_forward` (int, optional): Number of days forward to search (default: 30)
- **Returns**: JSON object with bookings array, total count, and metadata
- **Example Response**:
  ```json
  {
    "success": true,
    "message": "Found 3 bookings",
    "bookings": [...],
    "total_count": 3,
    "date_range": {...}
  }
  ```

### `create_booking`

- **Parameters**:
  - `event_type_id` (int): Event type ID from `list_event_types`
  - `start_time` (string): Start time in ISO format (e.g., "2024-01-15T14:00:00")
  - `attendee_name` (string): Full name of attendee
  - `attendee_email` (string): Email address of attendee
  - `timezone` (string): Timezone (e.g., "America/New_York", "UTC")
- **Returns**: JSON object with booking details and confirmation
- **Note**: End time is automatically calculated based on the event type duration

### `list_event_types`

- **Parameters**: None (returns all available event types)
- **Returns**: JSON object with event types array and metadata

### `check_availability`

- **Parameters**:
  - `event_type_id` (int): Event type ID from `list_event_types`
  - `date` (string): Date to check in YYYY-MM-DD format (e.g., "2024-01-15")
  - `timezone` (string): Timezone (e.g., "America/New_York", "UTC")
- **Returns**: JSON object with simplified time slots (HH:MM format) and metadata
- **Example Response**:
  ```json
  {
    "success": true,
    "message": "Found 5 available time slots on 2025-07-10",
    "available_times": ["09:00", "10:00", "11:00", "14:00", "15:00"],
    "total_slots": 5,
    "date": "2025-07-10",
    "timezone": "Asia/Calcutta",
    "note": "Times are shown in the requested timezone"
  }
  ```

## Docker

You can also run the server in Docker:

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "run_server.py"]
```

## Key Benefits

- **Standard MCP Protocol**: Built with official MCP framework for maximum compatibility
- **Professional Architecture**: Follows MCP best practices and design patterns
- **Comprehensive Tool Set**: 4 tools covering all booking management scenarios
- **JSON Schema Validation**: Proper input validation with detailed error messages
- **Structured Responses**: Consistent JSON responses with success/error handling
- **HTTP Session Management**: Streamable HTTP transport with CORS support
- **Cal.com Integration**: Full integration with Cal.com's v2 API

## Migration from FastMCP

This server has been migrated from FastMCP to the standard MCP framework. Key improvements:

- **Standard Protocol**: Now uses official MCP framework (v1.9.4+)
- **Better Tool Management**: Proper tool registration with JSON schemas
- **Enhanced Error Handling**: Standard MCP error responses
- **Professional Structure**: Organized codebase with constants and schemas
- **Future-Proof**: Compatible with all MCP-compliant tools and frameworks

To test the migration, run:

```bash
python test_migration.py
```

## License

MIT
