# Test dependencies for Cal.com MCP Server

# Core testing framework
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-cov>=4.0.0
pytest-timeout>=2.1.0

# HTTP testing
httpx>=0.25.0
responses>=0.23.0

# Async testing utilities
asynctest>=0.13.0

# Test data generation
factory-boy>=3.2.0
faker>=18.0.0

# Code quality and linting (for CI/CD)
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.0.0

# Coverage reporting
coverage>=7.0.0

# Environment management for tests
python-dotenv>=1.0.0

# Include main dependencies
-r requirements.txt