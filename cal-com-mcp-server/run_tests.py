#!/usr/bin/env python3
"""
Test runner script for Cal.com MCP Server

This script provides a convenient way to run tests with different configurations.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"❌ Command not found: {cmd[0]}")
        print("Make sure pytest is installed: pip install -r requirements-test.txt")
        return False


def main():
    parser = argparse.ArgumentParser(description="Run tests for Cal.com MCP Server")
    parser.add_argument(
        "--type",
        choices=["all", "unit", "integration", "fast", "slow"],
        default="all",
        help="Type of tests to run (default: all)",
    )
    parser.add_argument(
        "--coverage", action="store_true", help="Run tests with coverage reporting"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Run tests with verbose output"
    )
    parser.add_argument(
        "--file", help="Run tests from specific file (e.g., tests/test_server.py)"
    )
    parser.add_argument(
        "--function",
        help="Run specific test function (e.g., test_fetch_bookings_success)",
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install test dependencies before running tests",
    )
    parser.add_argument(
        "--lint", action="store_true", help="Run linting checks before tests"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel (requires pytest-xdist)",
    )

    args = parser.parse_args()

    # Check if we're in the right directory
    if not Path("tests").exists():
        print(
            "❌ Tests directory not found. Please run this script from the project root."
        )
        sys.exit(1)

    success = True

    # Install dependencies if requested
    if args.install_deps:
        if not run_command(
            [sys.executable, "-m", "pip", "install", "-r", "requirements-test.txt"],
            "Installing test dependencies",
        ):
            success = False

    # Run linting if requested
    if args.lint:
        lint_commands = [
            (
                [sys.executable, "-m", "black", "--check", "src", "tests"],
                "Code formatting check (black)",
            ),
            (
                [sys.executable, "-m", "isort", "--check-only", "src", "tests"],
                "Import sorting check (isort)",
            ),
            (
                [sys.executable, "-m", "flake8", "src", "tests"],
                "Linting check (flake8)",
            ),
        ]

        for cmd, desc in lint_commands:
            if not run_command(cmd, desc):
                print(f"⚠️  {desc} failed, but continuing with tests...")

    # Build pytest command
    pytest_cmd = [sys.executable, "-m", "pytest"]

    # Add verbosity
    if args.verbose:
        pytest_cmd.append("-v")

    # Add coverage
    if args.coverage:
        pytest_cmd.extend(
            [
                "--cov=src",
                "--cov-report=html",
                "--cov-report=term-missing",
                "--cov-report=xml",
            ]
        )

    # Add parallel execution
    if args.parallel:
        pytest_cmd.extend(["-n", "auto"])

    # Add test type filters
    if args.type == "unit":
        pytest_cmd.extend(["-m", "unit"])
    elif args.type == "integration":
        pytest_cmd.extend(["-m", "integration"])
    elif args.type == "fast":
        pytest_cmd.extend(["-m", "not slow"])
    elif args.type == "slow":
        pytest_cmd.extend(["-m", "slow"])

    # Add specific file or function
    if args.file:
        if args.function:
            pytest_cmd.append(f"{args.file}::{args.function}")
        else:
            pytest_cmd.append(args.file)
    elif args.function:
        pytest_cmd.extend(["-k", args.function])

    # Run tests
    if not run_command(pytest_cmd, f"Running {args.type} tests"):
        success = False

    # Summary
    print(f"\n{'='*60}")
    if success:
        print("🎉 All operations completed successfully!")
        if args.coverage:
            print("📊 Coverage report generated in htmlcov/index.html")
    else:
        print("💥 Some operations failed. Check the output above for details.")
    print(f"{'='*60}")

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
