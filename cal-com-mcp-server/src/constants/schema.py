"""
Pydantic schemas for Cal.com MCP server tools
"""

from pydantic import BaseModel, Field
from typing import Optional, List


# Booking management schemas
class ListBookings(BaseModel):
    """Schema for listing Cal.com bookings"""

    limit: Optional[int] = Field(
        default=100, description="Maximum number of bookings to return"
    )
    status: Optional[str] = Field(
        default=None,
        description="Filter by booking status (confirmed, cancelled, etc.)",
    )
    days_back: Optional[int] = Field(
        default=30, description="Number of days back to search"
    )
    days_forward: Optional[int] = Field(
        default=30, description="Number of days forward to search"
    )


class CreateBooking(BaseModel):
    """Schema for creating a new Cal.com booking"""

    event_type_id: int = Field(
        ..., description="Cal.com Event Type ID (get this from list_event_types)"
    )
    start_time: str = Field(
        ..., description="Start time in ISO format (e.g., '2024-01-15T14:00:00')"
    )
    attendee_name: str = Field(..., description="Full name of the attendee")
    attendee_email: str = Field(..., description="Email address of the attendee")
    timezone: str = Field(..., description="Timezone (e.g., 'America/New_York', 'UTC')")


class GetBooking(BaseModel):
    """Schema for getting a specific booking by UID"""

    booking_uid: str = Field(..., description="Unique identifier for the booking")


# Event type schemas
class ListEventTypes(BaseModel):
    """Schema for listing Cal.com event types - no parameters required"""

    pass


class GetEventType(BaseModel):
    """Schema for getting a specific event type"""

    event_type_id: int = Field(..., description="Cal.com Event Type ID")


# Availability schemas
class CheckAvailability(BaseModel):
    """Schema for checking availability for a Cal.com event type"""

    event_type_id: int = Field(
        ..., description="Cal.com Event Type ID (get this from list_event_types)"
    )
    date: str = Field(
        ..., description="Date to check in YYYY-MM-DD format (e.g., '2024-01-15')"
    )
    timezone: str = Field(
        ..., description="Timezone (e.g., 'America/New_York', 'Asia/Calcutta')"
    )
