from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class CalcomBooking(BaseModel):
    """Represents a Cal.com booking"""

    id: int
    title: str
    description: Optional[str] = None
    start_time: datetime
    end_time: datetime
    attendees: List[Dict[str, Any]] = []
    location: Optional[str] = None
    event_type_id: int
    status: str = "confirmed"
    uid: str


class CreateBookingRequest(BaseModel):
    """Simplified request model for creating a Cal.com booking"""

    event_type_id: int = Field(..., description="Cal.com Event Type ID")
    start_time: str = Field(
        ..., description="Start time in ISO format (e.g., '2024-01-15T14:00:00')"
    )
    attendee_name: str = Field(..., description="Attendee full name")
    attendee_email: str = Field(..., description="Attendee email address")
    timezone: str = Field(
        ..., description="Timezone (e.g., 'America/New_York', 'Asia/Calcutta')"
    )


class EventType(BaseModel):
    """Simplified Cal.com Event Type"""

    id: int
    title: str
    slug: Optional[str] = None
    length: int  # in minutes
    description: Optional[str] = None
    locations: List[Dict[str, Any]] = []


class BookingListResponse(BaseModel):
    """Simplified response model for listing bookings"""

    bookings: List[CalcomBooking]
    total_count: int
    message: str


class EventTypeListResponse(BaseModel):
    """Simplified response model for listing event types"""

    event_types: List[EventType]
    total_count: int
    message: str


class CreateBookingResponse(BaseModel):
    """Response model for booking creation"""

    success: bool
    booking_id: Optional[int] = None
    booking_uid: Optional[str] = None
    booking_url: Optional[str] = None
    message: str
    booking: Optional[CalcomBooking] = None
    error_code: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


class AvailabilitySlot(BaseModel):
    """Simplified available time slot"""

    time: str = Field(
        ...,
        description="Available time in ISO format (e.g., '2024-01-15T14:00:00')",
    )
    attendees: int = Field(0, description="Number of attendees for this slot")
    booking_url: Optional[str] = Field(None, description="Booking URL if available")


class AvailabilityRequest(BaseModel):
    """Request model for checking availability using Cal.com v2 API"""

    event_type_id: int = Field(..., description="Cal.com Event Type ID")
    date_from: str = Field(
        ...,
        description="Start date to check in YYYY-MM-DD format (e.g., '2024-01-15')",
    )
    date_to: Optional[str] = Field(
        None,
        description="End date to check in YYYY-MM-DD format (defaults to date_from if not provided)",
    )
    timezone: str = Field(
        ..., description="Timezone (e.g., 'America/New_York', 'Asia/Calcutta')"
    )


class AvailabilityResponse(BaseModel):
    """Response model for availability checking using Cal.com v2 API"""

    success: bool
    event_type_id: int
    date_from: str = Field(..., description="Start date checked in YYYY-MM-DD format")
    date_to: Optional[str] = Field(
        None, description="End date checked in YYYY-MM-DD format"
    )
    available_slots: List[AvailabilitySlot] = []
    timezone: str = Field(default="UTC", description="Timezone used for the check")
    message: str
    total_slots: int = Field(0, description="Total number of available slots")
