[tool:pytest]
# Pytest configuration file

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    asyncio: Async tests
    network: Tests that require network access
    
# Async test configuration
asyncio_mode = auto

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (if pytest-cov is installed)
# addopts = --cov=src --cov-report=html --cov-report=term-missing

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:httpx.*